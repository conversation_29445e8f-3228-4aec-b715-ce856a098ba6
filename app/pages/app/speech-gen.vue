<script setup lang="ts">
import { formatNumber } from '~/utils'
import {
  compareFileArrays,
  commonValidationRules
} from '~/utils/generationValidation'

const route = useRoute()
const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const { model, models, speed, outputFormat, outputChannel }
  = useSpeechGenModels()
const { selectedVoice, loadVoices } = useSpeechVoices()
const { selectedEmotion } = useSpeechEmotions()
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()

const textToSpeechStore = useTextToSpeechStore()
const {
  textToSpeechResult,
  aiToolSpeechCardRef,
  prompt,
  selectedFiles,
  supportFiles,
  hasSelectedFiles,
  uploadProgress,
  loadings,
  errors,
  supportFilesDisplay
} = storeToRefs(textToSpeechStore)

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  model: models[0],
  selectedVoice: null,
  selectedEmotion: null,
  speed: 1.0,
  outputFormat: 'mp3',
  outputChannel: 'mono',
  selectedFiles: [] as File[]
})

// Initialize initial values on mount
onMounted(() => {
  loadVoices()
  initialValues.value = {
    prompt: prompt.value,
    model: model.value,
    selectedVoice: selectedVoice.value,
    selectedEmotion: selectedEmotion.value,
    speed: speed.value,
    outputFormat: outputFormat.value,
    outputChannel: outputChannel.value,
    selectedFiles: [...selectedFiles.value]
  }

  // Set the ref in the store
  nextTick(() => {
    textToSpeechStore.aiToolSpeechCardRef = aiToolSpeechCardRef
  })
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = prompt.value !== initialValues.value.prompt
      || model.value?.value !== initialValues.value.model?.value
      || selectedVoice.value?.id !== initialValues.value.selectedVoice?.id
      || selectedEmotion.value?.emotion_key
      !== initialValues.value.selectedEmotion?.emotion_key
      || speed.value !== initialValues.value.speed
      || outputFormat.value !== initialValues.value.outputFormat
      || outputChannel.value !== initialValues.value.outputChannel

  // File comparison with better performance
  const filesChanged = compareFileArrays(
    selectedFiles.value,
    initialValues.value.selectedFiles
  )

  return basicFieldsChanged || filesChanged
})

const generateModeActive = computed({
  get() {
    return (route.query.mode as string) || 'text-to-speech'
  },
  set(mode) {
    // Hash is specified here to prevent the page from scrolling to the top
    router.push({
      path: '/app/speech-gen',
      query: { mode }
    })
  }
})

// Helper function to perform the actual generation
const performGeneration = async () => {
  let result
  if (generateModeActive.value === 'document-to-speech') {
    result = await handleDocumentToSpeech()
  } else {
    result = await handleTextToSpeech()
  }

  if (result) {
    toast.add({
      id: 'success',
      title: 'Speech Generation',
      description: 'Your speech is being generated. Please check back later.',
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      model: model.value,
      selectedVoice: selectedVoice.value,
      selectedEmotion: selectedEmotion.value,
      speed: speed.value,
      outputFormat: outputFormat.value,
      outputChannel: outputChannel.value,
      selectedFiles: [...selectedFiles.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    () => ({
      isValid: hasSelectedFiles.value || !!prompt.value?.trim(),
      message: t('Please enter text or select a file to generate speech.')
    }),
    commonValidationRules.requiredValue(
      selectedVoice.value,
      t('Please select a voice for speech generation.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'speech',
    hasChanges,
    hasResult: computed(() => !!textToSpeechResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const handleTextToSpeech = async () => {
  return await textToSpeechStore.textToSpeech({
    input: prompt.value,
    model: model.value.value,
    voices: [
      {
        name: selectedVoice.value?.speaker_name,
        voice: {
          id: selectedVoice.value?.id,
          name: selectedVoice.value?.speaker_name
        }
      }
    ],
    emotion: selectedEmotion.value?.emotion_key,
    speed: speed.value,
    output_format: outputFormat.value,
    output_channel: outputChannel.value
  })
}

const handleDocumentToSpeech = async () => {
  for (const file of selectedFiles.value) {
    return await textToSpeechStore.documentToSpeech(file, {
      input: prompt.value,
      model: model.value.value,
      emotion: selectedEmotion.value?.emotion_key,
      speed: speed.value,
      output_format: outputFormat.value,
      output_channel: outputChannel.value,
      voices: [
        {
          name: selectedVoice.value?.speaker_name,
          voice: {
            id: selectedVoice.value?.id,
            name: selectedVoice.value?.speaker_name
          }
        }
      ]
    })
  }
}

onMounted(() => {
  loadVoices()
})

const handleFilesSelected = (files: File[]) => {
  textToSpeechStore.selectedFiles = files
}

const generateModes = computed(() => {
  return [
    {
      label: t('Text to Speech'),
      icon: 'ion:language-sharp',
      value: 'text-to-speech'
    },
    {
      label: t('Document to Speech'),
      icon: 'basil:document-outline',
      value: 'document-to-speech'
    }
  ]
})
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <UTabs
            v-model="generateModeActive"
            :items="generateModes"
            class="w-full"
            color="neutral"
            :content="false"
            size="sm"
          />
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('modelPreset')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('emotion')"
              :label="$t('emotion')"
            >
              <BaseSpeechEmotionSelectModal
                v-model="selectedEmotion"
                size="sm"
              />
            </UFormField>
          </div>
          <UFormField
            v-if="generateModeActive === 'text-to-speech'"
            :label="$t('Text')"
          >
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="
                hasSelectedFiles
                  ? $t('Generate speech from selected file')
                  : $t(
                    'Start writing or paste your text here or select a file to generate speech...'
                  )
              "
              :rows="6"
            />
          </UFormField>
          <UFormField
            v-if="generateModeActive === 'document-to-speech'"
            class="flex flex-col gap-4"
            :label="$t('Upload Document')"
          >
            <BaseFiles
              v-if="hasSelectedFiles"
              v-model="selectedFiles"
              :upload-progress="uploadProgress"
              class="w-full"
            />
            <BaseFileSelect
              v-else
              v-model="selectedFiles"
              :support-files="supportFiles"
              :support-files-display="supportFilesDisplay"
              @update:model-value="handleFilesSelected"
            />
          </UFormField>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- <UFormField
              v-if="model?.options?.includes('voice')"
              :label="$t('voice')"
            >
              <BaseSpeechVoiceSelectModal
                v-model="selectedVoice"
                size="sm"
              />
            </UFormField> -->
            <UFormField
              v-if="
                model?.options?.some((option: string) =>
                  ['speed', 'outputFormat', 'outputChannel'].includes(option)
                )
              "
              :label="$t('settings')"
            >
              <BaseSpeechSettingsModal />
            </UFormField>
          </div>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div class="text-xs text-right">
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("This generation will cost: {cost} Credits", {
                    cost: 25
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('Generate Speech')"
              class="bg-gradient-to-r from-primary-500 to-violet-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-violet-600 cursor-pointer"
              trailing-icon="mingcute:ai-fill"
              :loading="loadings['textToSpeech']"
              :disabled="!prompt && !hasSelectedFiles"
              @click="onGenerate"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToSpeechResult || loadings['textToSpeech'])
            && !errors['textToSpeech']
        "
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolSpeechCard
          ref="aiToolSpeechCardRef"
          v-bind="textToSpeechResult"
          :data="textToSpeechResult"
          :loading="loadings['textToSpeech']"
          class="h-full"
        />
      </Motion>
      <VoicesLibraries v-else />
      <UCard
        v-if="false"
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <div>
            <UIcon
              :name="
                errors['textToSpeech']
                  ? 'i-lucide-alert-circle'
                  : 'i-lucide-mic'
              "
              class="text-6xl mb-2"
              :class="errors['textToSpeech'] ? 'text-error' : ''"
            />
          </div>
          <div
            v-if="errors['textToSpeech']"
            class="text-sm text-error"
          >
            {{ errors["textToSpeech"] }}
          </div>
          <div
            v-else
            class="text-sm"
          >
            {{ $t("Your generated speech will appear here") }}
          </div>
        </div>
      </UCard>
    </div>

    <!-- Speech Prompt Gallery -->
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.6,
        delay: 1.2
      }"
    >
      <AIToolSpeechPromptGallery
        class="mt-8"
        @prompt-selected="(selectedPrompt: string) => (prompt = selectedPrompt)"
      />
    </Motion>
  </UContainer>
</template>
