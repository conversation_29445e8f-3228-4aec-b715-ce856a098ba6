<template>
  <UCard>
    <UTabs
      v-model="voiceTypeActive"
      :items="voiceTypes"
      class="w-full"
      color="neutral"
      :content="false"
      size="sm"
    />
  </UCard>
</template>

<script setup lang="ts">
const { t } = useI18n()
const route = useRoute()
const router = useRouter()

const voiceTypes = computed(() => {
  return [
    {
      label: t('Gemini Voices'),
      value: 'gemini_voice',
      icon: 'ri:gemini-fill'
    },
    {
      label: t('System Voices'),
      value: 'system_voices',
      icon: 'hugeicons:voice'
    },
    {
      label: t('My Voices'),
      value: 'my_voices',
      icon: 'fluent:person-voice-24-filled'
    },
    {
      label: t('Favorite Voices'),
      value: 'favorite_voices',
      icon: 'mdi:puzzle-favorite'
    }
  ]
})

const voiceTypeActive = computed({
  get() {
    return (route.query.voiceType as string) || 'gemini_voice'
  },
  set(voiceType) {
    // Hash is specified here to prevent the page from scrolling to the top
    router.push({
      query: { voiceType }
    })
  }
})
</script>
