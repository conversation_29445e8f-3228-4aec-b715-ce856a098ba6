<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

const route = useRoute()
const router = useRouter()

const { getImageModelLabel } = useImageGenModels()
const { getPersonGenerationLabel } = usePersonGenerationOptions()
const { getSafetyFilterLabel } = useSafetyFilterOptions()
const historyStore = useHistoryStore()
const { showDetailModal, historyDetail, loadings, historyDetailUuid }
  = storeToRefs(historyStore)

const isHovered = ref(false)
const isTouchDevice = ref(false)

// Check if it's a touch device on component mount
onMounted(() => {
  isTouchDevice.value
    = 'ontouchstart' in window || navigator.maxTouchPoints > 0
})

watch(
  () => showDetailModal.value,
  (newValue) => {
    nextTick(() => {
      if (newValue) {
        nextTick(() => {
          historyStore.fetchHistoryDetail(
            historyDetailUuid.value || (route.query.uuid as string)
          )
        })
      } else {
        historyDetail.value = null
        historyDetailUuid.value = undefined
      }
    })
  }
)

onUnmounted(() => {
  // Cleanup if needed
  historyDetail.value = null
})

const generateWithPrompt = () => {
  // Implement the generate functionality here
  historyStore.cloneGeneration(historyDetail.value)
}

const firstImage = computed(() => {
  return historyDetail.value?.generated_image?.[0] || {}
})

const thumbnailImage = computed(() => {
  return firstImage.value?.thumbnails?.find(
    (thumb: any) => thumb.size === 'medium'
  )
})

const title = computed(() => {
  return historyDetail.value?.input_text
})

const style = computed(() => {
  return firstImage.value?.style || historyDetail.value?.style
})

const onCloseFullScreen = () => {
  showDetailModal.value = false
  isHovered.value = false
  // Clear the URL query parameter when closing
  router.push({ query: {} })
}

const lastGenerated = computed(() => {
  return (
    historyDetail.value?.generated_video?.[
      historyDetail.value?.generated_video?.length - 1
    ] || {}
  )
})

const linkDownload = computed(() => {
  if (historyDetail.value?.type === 'image') {
    return firstImage.value?.image_url
  }
  if (historyDetail.value?.type === 'video') {
    return lastGenerated.value?.video_url
  }
  return ''
})

const duration = computed(() => {
  const video = lastGenerated.value
  if (video && video.duration) {
    const minutes = Math.floor(video.duration / 60)
    const seconds = Math.floor(video.duration % 60)
    return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`
  }
  return '0:00'
})

// Computed properties for BaseInfo based on history type
const baseInfoProperties = computed(() => {
  const baseProps: Record<string, any> = {
    uuid: historyDetail.value?.uuid
  }

  // Common properties for all types
  if (historyDetail.value?.model_name) {
    baseProps.model = historyDetail.value.model_name
  }
  if (historyDetail.value?.used_credit) {
    baseProps.used_credit = historyDetail.value.used_credit
  }

  // Type-specific properties
  if (historyDetail.value?.type === 'image') {
    return {
      ...baseProps,
      style: style.value,
      aspectRatio: (historyDetail.value as any)?.aspect_ratio,
      personGeneration: getPersonGenerationLabel(
        (historyDetail.value as any)?.person_generation
      ),
      safety_filter_level: getSafetyFilterLabel(
        (historyDetail.value as any)?.safety_filter_level
      )
    }
  }

  if (historyDetail.value?.type === 'video') {
    return {
      ...baseProps,
      style: style.value,
      aspectRatio: (historyDetail.value as any)?.aspect_ratio,
      duration: duration.value
    }
  }

  // TTS types: tts-text, tts-document, tts-multi-speaker
  if (
    ['tts-text', 'tts-document', 'tts-multi-speaker'].includes(
      historyDetail.value?.type
    )
  ) {
    const ttsProps: Record<string, any> = {
      ...baseProps,
      voice: historyDetail.value?.voice || historyDetail.value?.speaker_name,
      speed: historyDetail.value?.speed,
      emotion: historyDetail.value?.emotion,
      output_format: historyDetail.value?.generate_result?.output_format,
      output_channel: historyDetail.value?.generate_result?.output_channel
    }

    // Additional properties for tts-document
    if (historyDetail.value?.type === 'tts-document') {
      ttsProps.file_name
        = historyDetail.value?.generate_result?.file_name_origin
      ttsProps.file_size = historyDetail.value?.file_size
        ? `${(historyDetail.value.file_size / 1024 / 1024).toFixed(2)} MB`
        : undefined
    }

    // Additional properties for tts-multi-speaker
    if (historyDetail.value?.type === 'tts-multi-speaker') {
      ttsProps.speakers_count
        = historyDetail.value?.generate_result?.voices?.length
      ttsProps.custom_prompt = historyDetail.value?.custom_prompt
    }

    return ttsProps
  }

  return baseProps
})
</script>

<template>
  <UModal
    v-model:open="showDetailModal"
    fullscreen
    :ui="{
      content: 'dark:bg-black/90 backdrop-blur-xl'
    }"
    @keydown.esc="showDetailModal = false"
  >
    <template #content>
      <div class="relative w-full h-full flex flex-col md:flex-row">
        <!-- Close button -->
        <UButton
          icon="i-lucide-x"
          color="neutral"
          variant="ghost"
          class="absolute cursor-pointer top-4 right-4 dark:text-white hover:bg-white/10 z-10"
          @click="onCloseFullScreen"
        />

        <!-- Left side: Image -->
        <div
          class="w-full md:w-2/3 lg:w-3/4 h-1/2 md:h-full flex items-center justify-center animate-fadeIn p-4"
        >
          <div
            v-if="loadings['fetchHistoryDetail']"
            class="w-full h-40 flex items-center justify-center"
          >
            <div
              class="text-gray-400 dark:text-gray-600 flex flex-col items-center"
            >
              <UIcon
                name="eos-icons:loading"
                class="w-8 h-8 mb-2"
              />
              {{ $t("Please wait a moment...") }}
            </div>
          </div>
          <div
            v-else-if="historyDetail?.status === 1"
            class="w-full h-40 flex items-center justify-center"
          >
            <div
              class="text-gray-400 dark:text-gray-600 flex flex-col items-center"
            >
              <UIcon
                name="eos-icons:loading"
                class="w-8 h-8 mb-2"
              />
              {{ $t("Generating...") }}
            </div>
          </div>
          <!-- Prevent click propagation on the image itself to avoid closing when clicking on the image -->
          <img
            v-else-if="historyDetail?.type === 'image' && firstImage?.image_url"
            :src="firstImage?.image_url"
            :alt="title"
            class="max-h-full max-w-full object-contain cursor-zoom-out animate-scaleIn shadow-2xl border border-white/10 rounded"
            @click.stop
          >
          <BaseVideoPlayer
            v-else-if="historyDetail?.type === 'video'"
            :data="historyDetail"
          />
          <WaveformPlayer
            v-else-if="
              ['tts-text', 'tts-document', 'tts-multi-speaker'].includes(
                historyDetail?.type
              )
            "
            :audio-url="historyDetail?.media_url"
            :fullscreen="true"
          />
          <div
            v-else
            class="w-full h-40 flex items-center justify-center"
          >
            <div
              class="text-gray-400 dark:text-gray-600 flex flex-col items-center"
            >
              <UIcon
                name="i-lucide-image-off"
                class="w-8 h-8 mb-2"
              />
              {{ $t("noImageAvailable") }}
            </div>
          </div>
        </div>
        <!-- Right side: Image information and Generate button -->
        <div
          class="w-full md:w-1/3 lg:w-1/4 h-1/2 md:h-full dark:bg-black/50 bg-muted backdrop-blur-md p-4 md:p-6 lg:p-8 flex flex-col overflow-y-auto animate-slideInRight"
          @click.stop
        >
          <div class="dark:text-white/80 mb-4 md:mb-6">
            <h3 class="dark:text-white text-base md:text-lg font-medium mb-2">
              {{ $t("promptDetails") }}
            </h3>
            <USkeleton
              v-if="loadings['historyDetail']"
              class="h-8 w-full"
            />
            <p
              class="text-xs md:text-sm mb-4 md:mb-6 overflow-y-auto bg-gray-200 p-2 rounded-lg dark:bg-gray-800/50 whitespace-break-spaces"
            >
              {{ title }}
            </p>

            <BaseInfo
              class="mb-4 md:mb-6"
              :properties="baseInfoProperties"
              :loading="loadings['fetchHistoryDetail']"
            />
          </div>

          <div class="mt-auto pt-2 flex flex-row gap-4 items-center">
            <template v-if="loadings['fetchHistoryDetail']">
              <USkeleton class="h-10 w-full" />
              <USkeleton class="h-10 w-10" />
            </template>
            <template v-else>
              <UChatPromptSubmit
                color="primary"
                :label="$t('generateWithPrompt')"
                class="cursor-pointer w-full justify-center bg-gradient-to-r from-primary-500 to-violet-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-violet-600"
                icon="mingcute:ai-fill"
                @click="generateWithPrompt"
              />
              <BaseDownloadButton :link="linkDownload" />
            </template>
          </div>
        </div>

        <!-- <div
          v-if="!loadings['fetchHistoryDetail']"
          class="absolute bottom-4 left-4 dark:text-white/70 text-xs md:text-sm hidden md:block"
        >
          {{ $t("clickToClose") }}
        </div> -->
      </div>
    </template>
  </UModal>
</template>

<style scoped>
.imagen {
  transition: transform 0.3s ease;
}

/* Apply scale effect to the image when the card is hovered */
:deep(.group:hover) .imagen {
  transform: scale(1.05);
}

/* For touch devices */
@media (hover: none) {
  :deep(.group:active) .imagen {
    transform: scale(1.05);
  }
}

/* Add line-clamp utility if not available in your Tailwind config */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation for modal content */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-scaleIn {
  animation: scaleIn 0.5s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
