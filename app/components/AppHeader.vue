<script setup lang="ts">
const nuxtApp = useNuxtApp()
const { activeHeadings, updateHeadings } = useScrollspy()
const { t } = useI18n()
const items = computed(() => [
  {
    label: t('Generate'),
    to: '/app'
  },
  {
    label: 'History',
    to: '/history'
  },
  {
    label: 'Pricing',
    to: '/pricing'
  }
])

nuxtApp.hooks.hookOnce('page:finish', () => {
  updateHeadings(
    [
      document.querySelector('#features'),
      document.querySelector('#pricing'),
      document.querySelector('#testimonials')
    ].filter(Boolean) as Element[]
  )
})

const appStore = useAppStore()

const { loading } = storeToRefs(appStore)

const authStore = useAuthStore()
const { user, user_credit } = storeToRefs(authStore)
</script>

<template>
  <UHeader>
    <template #left>
      <NuxtLink to="/">
        <div class="flex flex-row gap-2 items-center mr-4">
          <BaseLogo
            id="main-logo"
            :loading="loading"
            :class="{
              'logo-loading animate__pulse animate__infinite': loading
            }"
            class="animate__animated"
          />
          <BaseAppTitle
            class="justify-center text-center flex mx-auto !text-xl"
          />
        </div>
      </NuxtLink>
      <UNavigationMenu
        :items="items"
        variant="link"
        class="hidden lg:block"
      />
    </template>

    <template #right>
      <div class="flex flex-row gap-2 items-center">
        <div
          v-if="user"
          class="flex flex-row gap-4 items-center"
        >
          <!-- <UButton
            color="primary"
            variant="soft"
            trailing-icon="ic:baseline-plus"
            size="sm"
            class="hidden sm:block"
          >
            <div>
              {{ formatNumber(user_credit?.available_credit || 0) }}
              {{ $t("Credits") }}
            </div>
          </UButton> -->
          <div
            class="cursor-pointer hidden sm:flex items-center gap-1 text-xs dark:bg-primary-200/20 dark:hover:bg-primary-200/50 bg-primary-400/20 hover:bg-primary-400/50 px-3 py-1.5 rounded-md"
            @click="() => navigateTo('/buy-credits')"
          >
            <div class="!text-primary">
              {{ formatNumber(user_credit?.available_credit || 0) }}
            </div>
            <div>
              {{ $t("Credits") }}
            </div>
            <UIcon
              name="ic:baseline-plus"
              class="!text-primary text-lg"
            />
          </div>

          <NotificationBell />
          <AppUserMenu />
        </div>
        <div
          v-else
          class="flex flex-row gap-2 items-center"
        >
          <UButton
            :label="$t('Login')"
            variant="subtle"
            color="neutral"
            class="hidden lg:block"
            to="/auth/login"
          />
          <UButton
            :label="$t('ui.buttons.signUp')"
            variant="solid"
            class="hidden lg:block"
            to="/auth/signup"
          />
        </div>
      </div>
    </template>

    <template #body>
      <UNavigationMenu
        :items="items"
        orientation="vertical"
        class="-mx-2.5"
      />
      <UButton
        class="mt-4"
        :label="$t('ui.buttons.downloadApp')"
        variant="subtle"
        block
      />
    </template>
  </UHeader>
</template>
