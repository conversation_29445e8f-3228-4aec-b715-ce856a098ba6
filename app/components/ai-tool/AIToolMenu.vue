<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui'

const { t } = useI18n()
const route = useRoute()

const items = computed(
  () =>
    [
      {
        label: t('aiToolMenu.imagen'),
        icon: 'hugeicons:ai-image',
        slot: 'image' as const,
        to: '/app',
        exact: true,
        color: 'secondary'
      },
      {
        label: t('aiToolMenu.videoGen'),
        icon: 'hugeicons:ai-video',
        slot: 'components' as const,
        to: '/app/video-gen',
        color: 'success'
      },
      {
        label: t('aiToolMenu.speechGen'),
        icon: 'hugeicons:ai-voice',
        slot: 'components' as const,
        to: '/app/speech-gen',
        color: 'warning'
      },
      {
        label: t('speech.dialogueGeneration.dialogueGen'),
        icon: 'ri:chat-smile-ai-line',
        slot: 'components' as const,
        to: '/app/dialogue-gen',
        color: 'error'
      }
    ] satisfies NavigationMenuItem[]
)
</script>

<template>
  <div class="w-fit mx-auto">
    <div class="opacity-100 grid grid-cols-4 sm:grid-cols-4 gap-4">
      <UCard
        v-for="item in items"
        :key="item.label"
        :to="item.to"
        variant="subtle"
        class="group cursor-pointer scale-90 hover:scale-110 duration-200 transition-transform"
        :class="[
          route.path === item.to ? `scale-110` : ``
        ]"
        :ui="{
          body: '!px-4 !py-2'
        }"
        @click="navigateTo(item.to)"
      >
        <div
          :class="[
            route.path === item.to ? `text-${item.color}` : ``,
            `group-hover:text-${item.color}`
          ]"
          class="text-center flex flex-col justify-center items-center gap-2"
        >
          <UIcon
            :name="item.icon"
            class="w-6 h-6 sm:w-8 sm:h-8 duration-200 transition-transform group-hover:scale-125"
          />
          <div
            class="sm:text-xs text-[10px] duration-300 transition-transform"
            :class="[route.path === item.to ? `font-bold` : '']"
          >
            {{ item.label }}
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>
